<h2 id="user_genre_info">user_genre_info(user object)</h2>

```python
from letterboxdpy import user
user_instance = user.User("nmcassa")
print(user.user_genre_info(user_instance))
```

<details>
  <summary>Click to expand <code>user_genre_info</code> method response</summary>

```json
{
    "action":55,
    "adventure":101,
    "animation":95,
    "comedy":188,
    "crime":22,
    "documentary":16,
    "drama":94,
    "family":109,
    "fantasy":54,
    "history":5,
    "horror":27,
    "music":9,
    "mystery":30,
    "romance":29,
    "science-fiction":48,
    "thriller":43,
    "tv-movie":13,
    "war":4,
    "western":5
}
```
</details>