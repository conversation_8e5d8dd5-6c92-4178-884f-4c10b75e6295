{"entries": {"677133605": {"name": "The Substance", "slug": "the-substance", "id": "838140", "release": 2024, "runtime": 141, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "676822055": {"name": "Whiplash", "slug": "whiplash-2014", "id": "171384", "release": 2014, "runtime": 107, "actions": {"rewatched": true, "rating": 10, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "671009152": {"name": "Speak No Evil", "slug": "speak-no-evil-2024", "id": "1004531", "release": 2024, "runtime": 110, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "670124692": {"name": "Kindergarten Cop", "slug": "kindergarten-cop", "id": "51208", "release": 1990, "runtime": 111, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "669751864": {"name": "Doom", "slug": "doom", "id": "47597", "release": 2005, "runtime": 105, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "666283987": {"name": "Clerks", "slug": "clerks", "id": "50357", "release": 1994, "runtime": 92, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "663901961": {"name": "Blink Twice", "slug": "blink-twice", "id": "756042", "release": 2024, "runtime": 103, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 9, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "661698850": {"name": "Shaun of the Dead", "slug": "shaun-of-the-dead", "id": "51405", "release": 2004, "runtime": 99, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "656950844": {"name": "The Devil Wears Prada", "slug": "the-devil-wears-prada", "id": "51712", "release": 2006, "runtime": 109, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "654923452": {"name": "Alien", "slug": "alien", "id": "51714", "release": 1979, "runtime": 117, "actions": {"rewatched": true, "rating": 8, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "653727093": {"name": "Alien: <PERSON><PERSON><PERSON>", "slug": "alien-romulus", "id": "850459", "release": 2024, "runtime": 119, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "652010967": {"name": "Alien", "slug": "alien", "id": "51714", "release": 1979, "runtime": 117, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "650109052": {"name": "Upgrade", "slug": "upgrade", "id": "430633", "release": 2018, "runtime": 100, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 8, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "642931522": {"name": "Trap", "slug": "trap-2024", "id": "931164", "release": 2024, "runtime": 105, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 8, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "639450724": {"name": "The World's End", "slug": "the-worlds-end", "id": "86991", "release": 2013, "runtime": 109, "actions": {"rewatched": true, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 7, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "637839598": {"name": "Starship Troopers", "slug": "starship-troopers", "id": "51555", "release": 1997, "runtime": 129, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 7, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "633030376": {"name": "<PERSON><PERSON><PERSON>", "slug": "twisters", "id": "641608", "release": 2024, "runtime": 123, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 7, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "632337584": {"name": "Birdman or (The Unexpected Virtue of Ignorance)", "slug": "birdman-or-the-unexpected-virtue-of-ignorance", "id": "139795", "release": 2014, "runtime": 120, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 7, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "630450484": {"name": "Dr. <PERSON> or: How I Learned to Stop Worrying and Love the Bomb", "slug": "dr-strangelove-or-how-i-learned-to-stop-worrying-and-love-the-bomb", "id": "51218", "release": 1964, "runtime": 95, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 7, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "629404971": {"name": "All That Jazz", "slug": "all-that-jazz", "id": "41361", "release": 1979, "runtime": 123, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 7, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "624189413": {"name": "MaXXXine", "slug": "maxxxine", "id": "922858", "release": 2024, "runtime": 104, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 7, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "623147894": {"name": "La Haine", "slug": "la-haine", "id": "51684", "release": 1995, "runtime": 98, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 7, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "621893199": {"name": "Blade Runner 2049", "slug": "blade-runner-2049", "id": "265439", "release": 2017, "runtime": 164, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 7, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "620684283": {"name": "Speed Racer", "slug": "speed-racer", "id": "48018", "release": 2008, "runtime": 135, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "618965070": {"name": "Kinds of Kindness", "slug": "kinds-of-kindness", "id": "928261", "release": 2024, "runtime": 164, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "618315514": {"name": "They Cloned Tyrone", "slug": "they-cloned-tyrone", "id": "658906", "release": 2023, "runtime": 122, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "612416628": {"name": "Léon: The Professional", "slug": "leon-the-professional", "id": "51949", "release": 1994, "runtime": 111, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "612245929": {"name": "<PERSON><PERSON>", "slug": "looper", "id": "6599", "release": 2012, "runtime": 118, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "611327083": {"name": "Burning", "slug": "burning-2018", "id": "422035", "release": 2018, "runtime": 148, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "604283899": {"name": "<PERSON>", "slug": "carrie-1976", "id": "48049", "release": 1976, "runtime": 98, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 6, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "599460129": {"name": "Furiosa: A Mad Max Saga", "slug": "furiosa-a-mad-max-saga", "id": "705221", "release": 2024, "runtime": 149, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 5, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "598682229": {"name": "Mad Max: Fury Road", "slug": "mad-max-fury-road", "id": "62780", "release": 2015, "runtime": 121, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 5, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "596956228": {"name": "I Saw the TV Glow", "slug": "i-saw-the-tv-glow", "id": "772230", "release": 2024, "runtime": 100, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 5, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "593809314": {"name": "<PERSON>lma & Louise", "slug": "the<PERSON><PERSON><PERSON><PERSON>", "id": "50992", "release": 1991, "runtime": 130, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 5, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "592649824": {"name": "The Matrix", "slug": "the-matrix", "id": "51518", "release": 1999, "runtime": 136, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 5, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "589459602": {"name": "The Fall Guy", "slug": "the-fall-guy-2024", "id": "667550", "release": 2024, "runtime": 126, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 5, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "588510762": {"name": "Baby Reindeer", "slug": "baby-reindeer", "id": "1156611", "release": 2024, "runtime": 237, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 5, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "581065562": {"name": "SPY x FAMILY CODE: White", "slug": "spy-x-family-code-white", "id": "958439", "release": 2023, "runtime": 110, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "576567854": {"name": "Civil War", "slug": "civil-war-2024", "id": "834656", "release": 2024, "runtime": 109, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": true}, "date": {"year": 2024, "month": 4, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "574176114": {"name": "We Need to Talk About Kevin", "slug": "we-need-to-talk-about-kevin", "id": "57622", "release": 2011, "runtime": 113, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "572740431": {"name": "Waking Life", "slug": "waking-life", "id": "47363", "release": 2001, "runtime": 99, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "571324344": {"name": "<PERSON> Man", "slug": "monkey-man", "id": "488751", "release": 2024, "runtime": 121, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": true}, "date": {"year": 2024, "month": 4, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "571211699": {"name": "Dune: Part Two", "slug": "dune-part-two", "id": "617443", "release": 2024, "runtime": 167, "actions": {"rewatched": false, "rating": 10, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "568712021": {"name": "In Bruges", "slug": "in-bruges", "id": "47809", "release": 2008, "runtime": 108, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "567872745": {"name": "Burn After Reading", "slug": "burn-after-reading", "id": "49054", "release": 2008, "runtime": 96, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 4, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "565479772": {"name": "Dune", "slug": "dune-2021", "id": "371378", "release": 2021, "runtime": 155, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 3, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "563012693": {"name": "Late Night with the Devil", "slug": "late-night-with-the-devil", "id": "843415", "release": 2023, "runtime": 93, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 3, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "548142042": {"name": "Superbad", "slug": "superbad", "id": "47776", "release": 2007, "runtime": 113, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 3, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "546447018": {"name": "<PERSON>", "slug": "jackie-brown", "id": "51868", "release": 1997, "runtime": 154, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 3, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "544863832": {"name": "The Grand Tour: <PERSON>", "slug": "the-grand-tour-sand-job", "id": "1123682", "release": 2024, "runtime": 136, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/1/", "no": 1}}, "544553588": {"name": "<PERSON>", "slug": "john-wick", "id": "172076", "release": 2014, "runtime": 101, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "543534892": {"name": "The Ascent", "slug": "the-ascent", "id": "15466", "release": 1977, "runtime": 111, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "541659364": {"name": "Perfect Days", "slug": "perfect-days-2023", "id": "879229", "release": 2023, "runtime": 124, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "540915579": {"name": "Drive-Away Dolls", "slug": "drive-away-dolls", "id": "861250", "release": 2024, "runtime": 85, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "537456035": {"name": "The Iron Giant", "slug": "the-iron-giant", "id": "46280", "release": 1999, "runtime": 86, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "536029935": {"name": "Cure", "slug": "cure", "id": "28195", "release": 1997, "runtime": 111, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "535023600": {"name": "Close Encounters of the Third Kind", "slug": "close-encounters-of-the-third-kind", "id": "51312", "release": 1977, "runtime": 135, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "534179051": {"name": "Man on the Moon", "slug": "man-on-the-moon", "id": "50749", "release": 1999, "runtime": 118, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "531364149": {"name": "Argylle", "slug": "argylle", "id": "763160", "release": 2024, "runtime": 139, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "529006028": {"name": "Fallen Leaves", "slug": "fallen-leaves-2023", "id": "888054", "release": 2023, "runtime": 81, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "528065678": {"name": "Anatomy of a Fall", "slug": "anatomy-of-a-fall", "id": "822093", "release": 2023, "runtime": 151, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "527808713": {"name": "Liverpool", "slug": "liverpool", "id": "82344", "release": 2008, "runtime": 84, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 2, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "524202738": {"name": "PlayTime", "slug": "playtime", "id": "46424", "release": 1967, "runtime": 115, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "521620480": {"name": "The Zone of Interest", "slug": "the-zone-of-interest", "id": "398800", "release": 2023, "runtime": 105, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "521004508": {"name": "Maestro", "slug": "maestro-2023", "id": "453069", "release": 2023, "runtime": 129, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "519469533": {"name": "Society of the Snow", "slug": "society-of-the-snow", "id": "813840", "release": 2023, "runtime": 144, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "513520182": {"name": "Black Swan", "slug": "black-swan", "id": "20956", "release": 2010, "runtime": 109, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "512524578": {"name": "Gravity", "slug": "gravity-2013", "id": "16529", "release": 2013, "runtime": 91, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "509190765": {"name": "Death Race 2000", "slug": "death-race-2000", "id": "44012", "release": 1975, "runtime": 80, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "508709056": {"name": "The Conversation", "slug": "the-conversation", "id": "51529", "release": 1974, "runtime": 114, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "508559593": {"name": "Total Recall", "slug": "total-recall-2012", "id": "331", "release": 2012, "runtime": 118, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "506072802": {"name": "My Life as a <PERSON><PERSON><PERSON>i", "slug": "my-life-as-a-zucchini", "id": "327909", "release": 2016, "runtime": 66, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "505708868": {"name": "Neighbors", "slug": "neighbors-2014", "id": "140512", "release": 2014, "runtime": 97, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "502947123": {"name": "Airplane!", "slug": "airplane", "id": "51339", "release": 1980, "runtime": 88, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "502212161": {"name": "How to Train Your Dragon", "slug": "how-to-train-your-dragon", "id": "46460", "release": 2010, "runtime": 98, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2024, "month": 1, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "495592379": {"name": "Poor Things", "slug": "poor-things-2023", "id": "710352", "release": 2023, "runtime": 142, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": true}, "date": {"year": 2023, "month": 12, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "494479331": {"name": "National Lampoon's Christmas Vacation", "slug": "national-lampoons-christmas-vacation", "id": "48638", "release": 1989, "runtime": 97, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "494479079": {"name": "The Polar Express", "slug": "the-polar-express", "id": "48883", "release": 2004, "runtime": 100, "actions": {"rewatched": true, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "491835120": {"name": "The Day After Tomorrow", "slug": "the-day-after-tomorrow", "id": "51660", "release": 2004, "runtime": 124, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "490905584": {"name": "Wonka", "slug": "wonka", "id": "706083", "release": 2023, "runtime": 117, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "489615291": {"name": "Godzilla Minus One", "slug": "godzilla-minus-one", "id": "845706", "release": 2023, "runtime": 124, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "488414093": {"name": "Battle Royale", "slug": "battle-royale", "id": "49810", "release": 2000, "runtime": 113, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "488055220": {"name": "Neon Genesis Evangelion: The End of Evangelion", "slug": "neon-genesis-evangelion-the-end-of-evangelion", "id": "40311", "release": 1997, "runtime": 87, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "487535780": {"name": "Taken", "slug": "taken", "id": "47648", "release": 2008, "runtime": 94, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "486741555": {"name": "Tokyo Godfathers", "slug": "tokyo-godfathers", "id": "43921", "release": 2003, "runtime": 93, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "484949157": {"name": "<PERSON> Boy and the Heron", "slug": "the-boy-and-the-heron", "id": "438692", "release": 2023, "runtime": 124, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "482681144": {"name": "<PERSON><PERSON>", "slug": "saltburn", "id": "835774", "release": 2023, "runtime": 131, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 12, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "478999414": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "snowpiercer", "id": "88488", "release": 2013, "runtime": 127, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "475626175": {"name": "The Holdovers", "slug": "the-holdovers", "id": "755564", "release": 2023, "runtime": 133, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "475001880": {"name": "The Hunger Games: The Ballad of Songbirds & Snakes", "slug": "the-hunger-games-the-ballad-of-songbirds-snakes", "id": "619510", "release": 2023, "runtime": 157, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "475001564": {"name": "The Killer", "slug": "the-killer-2023", "id": "717654", "release": 2023, "runtime": 118, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "470558274": {"name": "Mystery Team", "slug": "mystery-team", "id": "26446", "release": 2009, "runtime": 97, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "469867130": {"name": "Whiplash", "slug": "whiplash-2014", "id": "171384", "release": 2014, "runtime": 107, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "468398535": {"name": "An American Werewolf in London", "slug": "an-american-werewolf-in-london", "id": "51338", "release": 1981, "runtime": 97, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 11, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "466519649": {"name": "Five Nights at Freddy's", "slug": "five-nights-at-freddys", "id": "436948", "release": 2023, "runtime": 110, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 31}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "464671439": {"name": "Us", "slug": "us-2019", "id": "390520", "release": 2019, "runtime": 116, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "464578941": {"name": "Child's Play", "slug": "childs-play", "id": "46087", "release": 1988, "runtime": 87, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "462712379": {"name": "Train to Busan", "slug": "train-to-busan", "id": "330912", "release": 2016, "runtime": 118, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "462569484": {"name": "Halloween", "slug": "halloween-1978", "id": "60298", "release": 1978, "runtime": 91, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "462569410": {"name": "The Nightmare Before Christmas", "slug": "the-nightmare-before-christmas", "id": "47119", "release": 1993, "runtime": 76, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/2/", "no": 2}}, "459113323": {"name": "<PERSON>: <PERSON><PERSON>b Your Enthusiasm", "slug": "larry-david-curb-your-enthusiasm", "id": "184916", "release": 1999, "runtime": 59, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "456623469": {"name": "The Wonderful Story of <PERSON>", "slug": "the-wonderful-story-of-henry-sugar", "id": "829364", "release": 2023, "runtime": 39, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "451747268": {"name": "Primer", "slug": "primer", "id": "2682", "release": 2004, "runtime": 77, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 10, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "451579905": {"name": "Fantastic Mr. <PERSON>", "slug": "fantastic-mr-fox", "id": "46344", "release": 2009, "runtime": 87, "actions": {"rewatched": true, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 9, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "450112095": {"name": "Get Out", "slug": "get-out-2017", "id": "353117", "release": 2017, "runtime": 104, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 9, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "447735107": {"name": "Dumb Money", "slug": "dumb-money", "id": "710073", "release": 2023, "runtime": 104, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 9, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "441813938": {"name": "Memories of Murder", "slug": "memories-of-murder", "id": "45312", "release": 2003, "runtime": 131, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 9, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "439106650": {"name": "The Lord of the Rings: The Fellowship of the Ring", "slug": "the-lord-of-the-rings-the-fellowship-of-the-ring", "id": "51930", "release": 2001, "runtime": 179, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 9, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "436667114": {"name": "The Death of Stalin", "slug": "the-death-of-stalin", "id": "337565", "release": 2017, "runtime": 107, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "436409443": {"name": "Teenage Mutant Ninja Turtles", "slug": "teenage-mutant-ninja-turtles-2014", "id": "80950", "release": 2014, "runtime": 101, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "435866687": {"name": "Anchorman: The Legend of Ron <PERSON>", "slug": "anchorman-the-legend-of-ron-burgundy", "id": "47637", "release": 2004, "runtime": 95, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "434799251": {"name": "Mad Max Beyond Thunderdome", "slug": "mad-max-beyond-thunderdome", "id": "47240", "release": 1985, "runtime": 107, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "432421607": {"name": "Oldboy", "slug": "oldboy", "id": "51454", "release": 2003, "runtime": 120, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "430499828": {"name": "Jurassic Park", "slug": "jurassic-park", "id": "51733", "release": 1993, "runtime": 127, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "427121798": {"name": "Teenage Mutant <PERSON> Turtles: <PERSON><PERSON>", "slug": "teenage-mutant-ninja-turtles-mutant-mayhem", "id": "542005", "release": 2023, "runtime": 100, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "426647064": {"name": "Spider-Man 2", "slug": "spider-man-2", "id": "51560", "release": 2004, "runtime": 127, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "426152596": {"name": "BlackBerry", "slug": "blackberry-2023", "id": "915230", "release": 2023, "runtime": 119, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "425191147": {"name": "Talk to Me", "slug": "talk-to-me-2022", "id": "908144", "release": 2022, "runtime": 95, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "424472847": {"name": "<PERSON><PERSON><PERSON>", "slug": "stalker", "id": "51062", "release": 1979, "runtime": 162, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 8, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "423206578": {"name": "Barbie", "slug": "barbie", "id": "277064", "release": 2023, "runtime": 114, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "421988381": {"name": "Collateral", "slug": "collateral", "id": "50995", "release": 2004, "runtime": 120, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "421394468": {"name": "J<PERSON><PERSON>", "slug": "jarhead", "id": "2693", "release": 2005, "runtime": 123, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "419018444": {"name": "Mission: Impossible – Fallout", "slug": "mission-impossible-fallout", "id": "283273", "release": 2018, "runtime": 147, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "417602761": {"name": "<PERSON><PERSON><PERSON>", "slug": "oppenheimer-2023", "id": "784328", "release": 2023, "runtime": 181, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "413604382": {"name": "<PERSON>: Live in Concert", "slug": "richard-pryor-live-in-concert", "id": "37594", "release": 1979, "runtime": 78, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "413228945": {"name": "<PERSON>: Bring the Pain", "slug": "chris-rock-bring-the-pain", "id": "40565", "release": 1996, "runtime": 58, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "412844218": {"name": "Nausicaä of the Valley of the Wind", "slug": "nausicaa-of-the-valley-of-the-wind", "id": "51969", "release": 1984, "runtime": 117, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "412341571": {"name": "Inglourious Basterds", "slug": "inglourious-basterds", "id": "41352", "release": 2009, "runtime": 153, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "411816780": {"name": "Synecdoche, New York", "slug": "synecdoche-new-york", "id": "49039", "release": 2008, "runtime": 124, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "411389571": {"name": "House", "slug": "house", "id": "35925", "release": 1977, "runtime": 88, "actions": {"rewatched": false, "rating": 10, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "411389348": {"name": "Three Billboards Outside Ebbing, Missouri", "slug": "three-billboards-outside-ebbing-missouri", "id": "291610", "release": 2017, "runtime": 115, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "411389040": {"name": "<PERSON><PERSON>", "slug": "jojo-rabbit", "id": "444600", "release": 2019, "runtime": 108, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "410771762": {"name": "No Country for Old Men", "slug": "no-country-for-old-men", "id": "48140", "release": 2007, "runtime": 122, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "408846592": {"name": "<PERSON> Jones and the Dial of Destiny", "slug": "indiana-jones-and-the-dial-of-destiny", "id": "265433", "release": 2023, "runtime": 155, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 7, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "405885641": {"name": "The Holy Mountain", "slug": "the-holy-mountain", "id": "47805", "release": 1973, "runtime": 113, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "405885366": {"name": "The Lighthouse", "slug": "the-lighthouse-2019", "id": "433863", "release": 2019, "runtime": 109, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "404476234": {"name": "Asteroid City", "slug": "asteroid-city", "id": "668555", "release": 2023, "runtime": 105, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "399831126": {"name": "The Little Mermaid", "slug": "the-little-mermaid-2023", "id": "379591", "release": 2023, "runtime": 135, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "397996097": {"name": "Hands on a Hardbody: The Documentary", "slug": "hands-on-a-hardbody-the-documentary", "id": "40224", "release": 1997, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "397571286": {"name": "The World's End", "slug": "the-worlds-end", "id": "86991", "release": 2013, "runtime": 109, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "396743494": {"name": "<PERSON><PERSON>", "slug": "smiley-face", "id": "44082", "release": 2007, "runtime": 84, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "396682065": {"name": "The Hitchhiker's Guide to the Galaxy", "slug": "the-hitchhikers-guide-to-the-galaxy-2005", "id": "48023", "release": 2005, "runtime": 109, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "396426288": {"name": "Spider-Man: Across the Spider-Verse", "slug": "spider-man-across-the-spider-verse", "id": "497631", "release": 2023, "runtime": 140, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 6, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "394839627": {"name": "White Men Can't Jump", "slug": "white-men-cant-jump", "id": "46491", "release": 1992, "runtime": 115, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "393593302": {"name": "White Men Can't Jump", "slug": "white-men-cant-jump-2023", "id": "825993", "release": 2023, "runtime": 101, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "393190809": {"name": "Moneyball", "slug": "moneyball", "id": "6286", "release": 2011, "runtime": 134, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "393114864": {"name": "The Big Lebowski", "slug": "the-big-le<PERSON><PERSON>", "id": "51935", "release": 1998, "runtime": 117, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "390017765": {"name": "Fool's Paradise", "slug": "fools-paradise-2023", "id": "481889", "release": 2023, "runtime": 99, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "389880154": {"name": "Solar<PERSON>", "slug": "solaris", "id": "51528", "release": 1972, "runtime": 167, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "387282156": {"name": "<PERSON><PERSON>", "slug": "ponyo", "id": "44594", "release": 2008, "runtime": 100, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/3/", "no": 3}}, "385468293": {"name": "The Breakfast Club", "slug": "the-breakfast-club", "id": "50518", "release": 1985, "runtime": 98, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "385189455": {"name": "Si<PERSON>", "slug": "sisu-2022", "id": "755504", "release": 2022, "runtime": 91, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": true}, "date": {"year": 2023, "month": 5, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "385068876": {"name": "Buffalo '66", "slug": "buffalo-66", "id": "47133", "release": 1998, "runtime": 110, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "384453431": {"name": "<PERSON>", "slug": "beau-is-afraid", "id": "715856", "release": 2023, "runtime": 179, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 5, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "377582850": {"name": "<PERSON><PERSON> and the Bandit", "slug": "smokey-and-the-bandit", "id": "45696", "release": 1977, "runtime": 96, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "377453455": {"name": "Kajillionaire", "slug": "kajillionaire", "id": "470369", "release": 2020, "runtime": 105, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "376226026": {"name": "The Super Mario Bros. Movie", "slug": "the-super-mario-bros-movie", "id": "432302", "release": 2023, "runtime": 93, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "375230318": {"name": "Lost in Translation", "slug": "lost-in-translation", "id": "51898", "release": 2003, "runtime": 102, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "372292664": {"name": "Air", "slug": "air-2023", "id": "868491", "release": 2023, "runtime": 111, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "371969937": {"name": "The Summit of the Gods", "slug": "the-summit-of-the-gods", "id": "635689", "release": 2021, "runtime": 95, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 4, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "371397958": {"name": "<PERSON>: Chapter 4", "slug": "john-wick-chapter-4", "id": "530882", "release": 2023, "runtime": 170, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "370758321": {"name": "My Neighbor Totoro", "slug": "my-neighbor-totoro", "id": "47756", "release": 1988, "runtime": 86, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "366348073": {"name": "Inside", "slug": "inside-2023", "id": "861930", "release": 2023, "runtime": 106, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "365923435": {"name": "65", "slug": "65", "id": "624070", "release": 2023, "runtime": 92, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "365243568": {"name": "Being <PERSON>", "slug": "being-john-<PERSON><PERSON><PERSON>", "id": "51618", "release": 1999, "runtime": 113, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "360794800": {"name": "<PERSON>", "slug": "jerry-maguire", "id": "47206", "release": 1996, "runtime": 139, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "359884552": {"name": "<PERSON> III", "slug": "creed-iii", "id": "601624", "release": 2023, "runtime": 116, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 3, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "357803760": {"name": "Cocaine Bear", "slug": "cocaine-bear", "id": "721333", "release": 2023, "runtime": 95, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "356336710": {"name": "The Incredibles", "slug": "the-incredibles", "id": "46806", "release": 2004, "runtime": 115, "actions": {"rewatched": true, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "354356747": {"name": "Edge of Tomorrow", "slug": "edge-of-tomorrow", "id": "105183", "release": 2014, "runtime": 114, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "352003964": {"name": "Mars Attacks!", "slug": "mars-attacks", "id": "51975", "release": 1996, "runtime": 106, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "350548418": {"name": "Vivarium", "slug": "vivarium", "id": "390139", "release": 2019, "runtime": 97, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "350465044": {"name": "You People", "slug": "you-people-2023", "id": "780321", "release": 2023, "runtime": 117, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "348980416": {"name": "Knock at the Cabin", "slug": "knock-at-the-cabin", "id": "558056", "release": 2023, "runtime": 100, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 2, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "347318246": {"name": "Eternal Sunshine of the Spotless Mind", "slug": "eternal-sunshine-of-the-spotless-mind", "id": "2683", "release": 2004, "runtime": 108, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "346415171": {"name": "Funny Games", "slug": "funny-games", "id": "46418", "release": 1997, "runtime": 109, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "345265942": {"name": "The Sting", "slug": "the-sting", "id": "47315", "release": 1973, "runtime": 129, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "343609166": {"name": "Indiana Jones and the Temple of Doom", "slug": "indiana-jones-and-the-temple-of-doom", "id": "51963", "release": 1984, "runtime": 118, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "343260392": {"name": "Police Story", "slug": "police-story", "id": "47388", "release": 1985, "runtime": 99, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "342586706": {"name": "Tampopo", "slug": "tampopo", "id": "44970", "release": 1985, "runtime": 115, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "342081021": {"name": "Broker", "slug": "broker-2022", "id": "658830", "release": 2022, "runtime": 129, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "341829211": {"name": "<PERSON>", "slug": "rocky-iii", "id": "51085", "release": 1982, "runtime": 100, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "340549332": {"name": "Puss in Boots: The Last Wish", "slug": "puss-in-boots-the-last-wish", "id": "242285", "release": 2022, "runtime": 103, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "340130952": {"name": "Den of Thieves", "slug": "den-of-thieves", "id": "381754", "release": 2018, "runtime": 140, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "338849515": {"name": "Palm Springs", "slug": "palm-springs-2020", "id": "515467", "release": 2020, "runtime": 90, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "337297290": {"name": "<PERSON> II", "slug": "rocky-ii", "id": "51089", "release": 1979, "runtime": 119, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "336723611": {"name": "Clue", "slug": "clue", "id": "42531", "release": 1985, "runtime": 94, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "336146481": {"name": "The Running Man", "slug": "the-running-man", "id": "51287", "release": 1987, "runtime": 101, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "336071930": {"name": "Zombieland", "slug": "zombieland", "id": "39352", "release": 2009, "runtime": 88, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "336071564": {"name": "Je<PERSON><PERSON>: <PERSON><PERSON><PERSON>", "slug": "jer<PERSON>-car<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "id": "856484", "release": 2022, "runtime": 55, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "334346919": {"name": "Reservoir Dogs", "slug": "reservoir-dogs", "id": "51610", "release": 1992, "runtime": 99, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "333622654": {"name": "<PERSON>", "slug": "rocky", "id": "51090", "release": 1976, "runtime": 120, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "333344599": {"name": "Babylon", "slug": "babylon-2022", "id": "542773", "release": 2022, "runtime": 189, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "332446661": {"name": "Dope", "slug": "dope-2015", "id": "235745", "release": 2015, "runtime": 103, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "332289592": {"name": "The Gift", "slug": "the-gift-2015-1", "id": "255927", "release": 2015, "runtime": 108, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2023, "month": 1, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "331363183": {"name": "White Noise", "slug": "white-noise-2022", "id": "666269", "release": 2022, "runtime": 136, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "330144601": {"name": "Eyes Wide Shut", "slug": "eyes-wide-shut", "id": "51717", "release": 1999, "runtime": 159, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "329338634": {"name": "Rush Hour", "slug": "rush-hour", "id": "50517", "release": 1998, "runtime": 98, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "326887451": {"name": "Die Hard", "slug": "die-hard", "id": "51556", "release": 1988, "runtime": 132, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "326536673": {"name": "The Nice Guys", "slug": "the-nice-guys", "id": "216301", "release": 2016, "runtime": 116, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/4/", "no": 4}}, "325616538": {"name": "Men in Black", "slug": "men-in-black", "id": "51514", "release": 1997, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "325568544": {"name": "Entergalactic", "slug": "entergalactic", "id": "925555", "release": 2022, "runtime": 94, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "324948388": {"name": "Swiss Army Man", "slug": "swiss-army-man", "id": "277413", "release": 2016, "runtime": 97, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "323718943": {"name": "World of Tomorrow", "slug": "world-of-tomorrow", "id": "230808", "release": 2015, "runtime": 17, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "322951257": {"name": "Prisoners", "slug": "prisoners", "id": "110740", "release": 2013, "runtime": 153, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "321873074": {"name": "Violent Night", "slug": "violent-night", "id": "809395", "release": 2022, "runtime": 111, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "321716181": {"name": "The Life Aquatic with <PERSON>", "slug": "the-life-aquatic-with-steve-zissou", "id": "51674", "release": 2004, "runtime": 119, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "321060321": {"name": "Bullet Train", "slug": "bullet-train", "id": "641961", "release": 2022, "runtime": 126, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "320639237": {"name": "The Big Sick", "slug": "the-big-sick", "id": "350384", "release": 2017, "runtime": 120, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "319746191": {"name": "Another Round", "slug": "another-round", "id": "508246", "release": 2020, "runtime": 117, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 12, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "319230379": {"name": "<PERSON>", "slug": "klaus", "id": "438751", "release": 2019, "runtime": 96, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "318945520": {"name": "The Redeem Team", "slug": "the-redeem-team", "id": "917780", "release": 2022, "runtime": 98, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "318604511": {"name": "This Is the End", "slug": "this-is-the-end", "id": "87873", "release": 2013, "runtime": 106, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "318242981": {"name": "Glass Onion", "slug": "glass-onion", "id": "586723", "release": 2022, "runtime": 140, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "317698319": {"name": "Charlie and the Chocolate Factory", "slug": "charlie-and-the-chocolate-factory", "id": "51932", "release": 2005, "runtime": 115, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "317316870": {"name": "Kill Bill: Vol. 2", "slug": "kill-bill-vol-2", "id": "51696", "release": 2004, "runtime": 136, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "316754400": {"name": "Decision to Leave", "slug": "decision-to-leave", "id": "629320", "release": 2022, "runtime": 138, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "315452718": {"name": "The Thing", "slug": "the-thing", "id": "51155", "release": 1982, "runtime": 109, "actions": {"rewatched": false, "rating": 10, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "314903617": {"name": "The Menu", "slug": "the-menu-2022", "id": "521323", "release": 2022, "runtime": 107, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "314368605": {"name": "The Greatest Beer Run Ever", "slug": "the-greatest-beer-run-ever", "id": "525274", "release": 2022, "runtime": 126, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "312775894": {"name": "Kill Bill: Vol. 1", "slug": "kill-bill-vol-1", "id": "2694", "release": 2003, "runtime": 111, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "312193535": {"name": "Werewolf by Night", "slug": "werewolf-by-night", "id": "805106", "release": 2022, "runtime": 55, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "312193678": {"name": "<PERSON> & the Olympians: The Lightning Thief", "slug": "percy-jackson-the-olympians-the-lightning-thief", "id": "30264", "release": 2010, "runtime": 118, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "310491146": {"name": "Spirited Away", "slug": "spirited-away", "id": "51921", "release": 2001, "runtime": 125, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 11, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "309787331": {"name": "Triangle of Sadness", "slug": "triangle-of-sadness", "id": "427970", "release": 2022, "runtime": 147, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "307594717": {"name": "Do the Right Thing", "slug": "do-the-right-thing", "id": "51228", "release": 1989, "runtime": 120, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "306344794": {"name": "Best in Show", "slug": "best-in-show", "id": "43601", "release": 2000, "runtime": 90, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "306344637": {"name": "Risky Business", "slug": "risky-business", "id": "47248", "release": 1983, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "304439626": {"name": "Barbarian", "slug": "barbarian-2022", "id": "819648", "release": 2022, "runtime": 103, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "304081332": {"name": "Spirited Away", "slug": "spirited-away", "id": "51921", "release": 2001, "runtime": 125, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "302463246": {"name": "Amsterdam", "slug": "amsterdam-2022", "id": "589317", "release": 2022, "runtime": 134, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "302135176": {"name": "Jurassic Park III", "slug": "jurassic-park-iii", "id": "51731", "release": 2001, "runtime": 92, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 10, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "298301225": {"name": "X", "slug": "x-2022", "id": "680358", "release": 2022, "runtime": 106, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "297853891": {"name": "Galaxy Quest", "slug": "galaxy-quest", "id": "51227", "release": 1999, "runtime": 101, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "296780222": {"name": "Catch Me If You Can", "slug": "catch-me-if-you-can-2002", "id": "51484", "release": 2002, "runtime": 141, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "296198710": {"name": "See How They Run", "slug": "see-how-they-run-2022", "id": "686510", "release": 2022, "runtime": 99, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "295701500": {"name": "WarGames", "slug": "wargames", "id": "51292", "release": 1983, "runtime": 114, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "295493722": {"name": "Three Thousand Years of Longing", "slug": "three-thousand-years-of-longing", "id": "485265", "release": 2022, "runtime": 108, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "293024601": {"name": "War Dogs", "slug": "war-dogs-2016", "id": "235388", "release": 2016, "runtime": 114, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "292362029": {"name": "Spider-Man", "slug": "spider-man", "id": "51561", "release": 2002, "runtime": 121, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 9, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "291102357": {"name": "Only Yesterday", "slug": "only-yesterday", "id": "42613", "release": 1991, "runtime": 119, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 8, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "289681424": {"name": "Beast", "slug": "beast-2022-1", "id": "681132", "release": 2022, "runtime": 93, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": true}, "date": {"year": 2022, "month": 8, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "289439442": {"name": "Clueless", "slug": "clueless", "id": "46998", "release": 1995, "runtime": 97, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 8, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "287386987": {"name": "Bodies Bodies Bodies", "slug": "bodies-bodies-bodies", "id": "449442", "release": 2022, "runtime": 94, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 8, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "282971885": {"name": "Kiki's Delivery Service", "slug": "kikis-delivery-service", "id": "41360", "release": 1989, "runtime": 103, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 8, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "282206654": {"name": "<PERSON>'s Body", "slug": "jennifers-body", "id": "39305", "release": 2009, "runtime": 102, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "281707119": {"name": "Perfect Blue", "slug": "perfect-blue", "id": "46175", "release": 1997, "runtime": 82, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "281455809": {"name": "Coco", "slug": "coco-2017", "id": "285509", "release": 2017, "runtime": 105, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "281434926": {"name": "<PERSON><PERSON>", "slug": "mulan", "id": "46001", "release": 1998, "runtime": 88, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "281314210": {"name": "Joker", "slug": "joker-2019", "id": "406775", "release": 2019, "runtime": 122, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/5/", "no": 5}}, "279611898": {"name": "<PERSON>: <PERSON><PERSON>", "slug": "donald-glover-weirdo", "id": "70616", "release": 2012, "runtime": 65, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "279518894": {"name": "Nope", "slug": "nope", "id": "682547", "release": 2022, "runtime": 130, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "279074440": {"name": "<PERSON> the <PERSON> with Shoes On", "slug": "marcel-the-shell-with-shoes-on-2021", "id": "781326", "release": 2021, "runtime": 90, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "277626135": {"name": "<PERSON>: Paper Tiger", "slug": "bill-burr-paper-tiger", "id": "551637", "release": 2019, "runtime": 67, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "275832320": {"name": "Boogie Nights", "slug": "boogie-nights", "id": "49007", "release": 1997, "runtime": 156, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "275420800": {"name": "Thor: Love and Thunder", "slug": "thor-love-and-thunder", "id": "543002", "release": 2022, "runtime": 119, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "274963616": {"name": "<PERSON>ro Dreams of Sushi", "slug": "jiro-dreams-of-sushi", "id": "67195", "release": 2011, "runtime": 82, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "273241440": {"name": "Sorry to <PERSON><PERSON>", "slug": "sorry-to-bother-you", "id": "358271", "release": 2018, "runtime": 112, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 7, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "273005046": {"name": "<PERSON><PERSON>", "slug": "spiderhead", "id": "542567", "release": 2022, "runtime": 107, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 6, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "268665308": {"name": "Napoleon Dynamite", "slug": "napoleon-dynamite", "id": "47867", "release": 2004, "runtime": 95, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 6, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "267562529": {"name": "<PERSON><PERSON><PERSON>", "slug": "hustle-2022", "id": "629376", "release": 2022, "runtime": 117, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 6, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "*********": {"name": "Luca", "slug": "luca-2021", "id": "438722", "release": 2021, "runtime": 95, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 31}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "*********": {"name": "Taxi Driver", "slug": "taxi-driver", "id": "51947", "release": 1976, "runtime": 114, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "*********": {"name": "Office Space", "slug": "office-space", "id": "50991", "release": 1999, "runtime": 90, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "*********": {"name": "Men", "slug": "men-2022", "id": "699298", "release": 2022, "runtime": 100, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": true}, "date": {"year": 2022, "month": 5, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "*********": {"name": "<PERSON> <PERSON><PERSON> Dale: Rescue Rangers", "slug": "chip-n-dale-rescue-rangers", "id": "354541", "release": 2022, "runtime": 99, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "260385332": {"name": "Old", "slug": "old-2021", "id": "558054", "release": 2021, "runtime": 108, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "258239312": {"name": "Phantom Thread", "slug": "phantom-thread", "id": "335384", "release": 2017, "runtime": 130, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "257992407": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "id": "50558", "release": 2007, "runtime": 111, "actions": {"rewatched": true, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "257580819": {"name": "Nightcrawler", "slug": "nightcrawler", "id": "169907", "release": 2014, "runtime": 118, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": true}, "date": {"year": 2022, "month": 5, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "257271467": {"name": "American Psycho", "slug": "american-psycho", "id": "51097", "release": 2000, "runtime": 102, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 5, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "256661991": {"name": "Perfect Bid: The Contestant Who Knew Too Much", "slug": "perfect-bid-the-contestant-who-knew-too-much", "id": "422385", "release": 2017, "runtime": 72, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "254152625": {"name": "Parasite", "slug": "parasite-2019", "id": "426406", "release": 2019, "runtime": 133, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "254152553": {"name": "Over the Garden Wall", "slug": "over-the-garden-wall-2014", "id": "343911", "release": 2014, "runtime": 109, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "253938106": {"name": "Blade Runner", "slug": "blade-runner", "id": "51972", "release": 1982, "runtime": 118, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "253222485": {"name": "The Ten Commandments", "slug": "the-ten-commandments-1956", "id": "48188", "release": 1956, "runtime": 220, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "252189453": {"name": "Everything Everywhere All at Once", "slug": "everything-everywhere-all-at-once", "id": "474474", "release": 2022, "runtime": 140, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "252049863": {"name": "Punch-<PERSON><PERSON>", "slug": "punch-drunk-love", "id": "47914", "release": 2002, "runtime": 96, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "250907762": {"name": "Her", "slug": "her", "id": "114564", "release": 2013, "runtime": 126, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "250131298": {"name": "The Power of the Dog", "slug": "the-power-of-the-dog", "id": "527841", "release": 2021, "runtime": 126, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "249075128": {"name": "<PERSON> 20th Anniversary: Return to Hogwarts", "slug": "harry-potter-20th-anniversary-return-to-hogwarts", "id": "809443", "release": 2022, "runtime": 103, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "249075068": {"name": "<PERSON> and the Deathly Hallows: Part 2", "slug": "harry-potter-and-the-deathly-hallows-part-2", "id": "44580", "release": 2011, "runtime": 130, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 4, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "248661439": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "caddyshack", "id": "44831", "release": 1980, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 31}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "248983663": {"name": "<PERSON> and the Deathly Hallows: Part 1", "slug": "harry-potter-and-the-deathly-hallows-part-1", "id": "44581", "release": 2010, "runtime": 146, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "247873103": {"name": "<PERSON> and the Half-Blood Prince", "slug": "harry-potter-and-the-half-blood-prince", "id": "51385", "release": 2009, "runtime": 153, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "246752035": {"name": "<PERSON> and the Order of the Phoenix", "slug": "harry-potter-and-the-order-of-the-phoenix", "id": "51449", "release": 2007, "runtime": 138, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "246293200": {"name": "<PERSON> and the Goblet of Fire", "slug": "harry-potter-and-the-goblet-of-fire", "id": "51450", "release": 2005, "runtime": 157, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "245945490": {"name": "<PERSON> and the Prisoner of Azkaban", "slug": "harry-potter-and-the-prisoner-of-az<PERSON>ban", "id": "51451", "release": 2004, "runtime": 141, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "244599567": {"name": "<PERSON> and the Chamber of Secrets", "slug": "harry-potter-and-the-chamber-of-secrets", "id": "51452", "release": 2002, "runtime": 161, "actions": {"rewatched": true, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "244268154": {"name": "The Batman", "slug": "the-batman", "id": "348914", "release": 2022, "runtime": 177, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "244099871": {"name": "<PERSON> and the Philosopher's Stone", "slug": "harry-potter-and-the-philosophers-stone", "id": "51453", "release": 2001, "runtime": 152, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "242322221": {"name": "The Shape of Water", "slug": "the-shape-of-water", "id": "333308", "release": 2017, "runtime": 123, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "242190614": {"name": "<PERSON>", "slug": "a<PERSON>ra", "id": "51902", "release": 1988, "runtime": 124, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "241793326": {"name": "Texas Chainsaw Massacre", "slug": "texas-chainsaw-massacre", "id": "558956", "release": 2022, "runtime": 83, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "240926182": {"name": "jeen-yuhs: A Kanye Trilogy", "slug": "jeen-yuhs-a-kanye-trilogy", "id": "836998", "release": 2022, "runtime": 270, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "240599065": {"name": "Juno", "slug": "juno", "id": "48051", "release": 2007, "runtime": 96, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 3, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "240348840": {"name": "Arrival", "slug": "arrival-2016", "id": "257426", "release": 2016, "runtime": 116, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "240093938": {"name": "The King of Comedy", "slug": "the-king-of-comedy", "id": "51794", "release": 1982, "runtime": 109, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "239115871": {"name": "Little Miss Sunshine", "slug": "little-miss-sunshine", "id": "51379", "release": 2006, "runtime": 102, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "238414703": {"name": "Booksmart", "slug": "booksmart", "id": "435450", "release": 2019, "runtime": 102, "actions": {"rewatched": true, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/6/", "no": 6}}, "237666718": {"name": "Scooby-Doo", "slug": "scooby-doo", "id": "46964", "release": 2002, "runtime": 87, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "237082796": {"name": "<PERSON><PERSON>", "slug": "pierrot-le-fou", "id": "50090", "release": 1965, "runtime": 110, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "236651328": {"name": "<PERSON><PERSON>", "slug": "marry-me-2022", "id": "542727", "release": 2022, "runtime": 112, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "235471233": {"name": "Some Kind of Heaven", "slug": "some-kind-of-heaven", "id": "564733", "release": 2020, "runtime": 83, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "235356607": {"name": "Untold: <PERSON><PERSON> at the Palace", "slug": "untold-malice-at-the-palace", "id": "771839", "release": 2021, "runtime": 69, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "235261550": {"name": "Fight Club", "slug": "fight-club", "id": "51568", "release": 1999, "runtime": 139, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "234981110": {"name": "All Hands on Deck!", "slug": "all-hands-on-deck-2020", "id": "590589", "release": 2020, "runtime": 95, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "233556897": {"name": "Moving On", "slug": "moving-on-2020", "id": "562602", "release": 2019, "runtime": 105, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "233335167": {"name": "Beats", "slug": "beats-2019", "id": "423722", "release": 2019, "runtime": 100, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "233051308": {"name": "The Royal Tenenbaums", "slug": "the-royal-tenenbaums", "id": "47169", "release": 2001, "runtime": 110, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232962216": {"name": "Luzzu", "slug": "luzzu", "id": "695499", "release": 2021, "runtime": 94, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 2, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232836398": {"name": "Labyrinth of Cinema", "slug": "labyrinth-of-cinema", "id": "543380", "release": 2019, "runtime": 179, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 31}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232558226": {"name": "Taming the Garden", "slug": "taming-the-garden", "id": "695509", "release": 2021, "runtime": 92, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232418025": {"name": "The Graduate", "slug": "the-graduate", "id": "27183", "release": 1967, "runtime": 106, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": true}, "date": {"year": 2022, "month": 1, "day": 30}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232215728": {"name": "Lemon", "slug": "lemon-2017", "id": "361916", "release": 2017, "runtime": 83, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232177460": {"name": "The Ballad of <PERSON>", "slug": "the-ballad-of-buster-scruggs", "id": "467061", "release": 2018, "runtime": 132, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "232108240": {"name": "It Follows", "slug": "it-follows", "id": "193554", "release": 2014, "runtime": 101, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "229784011": {"name": "The Colony", "slug": "the-colony-2021", "id": "681177", "release": 2021, "runtime": 104, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "228918866": {"name": "Howl's Moving Castle", "slug": "howls-moving-castle", "id": "49062", "release": 2004, "runtime": 119, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "228421988": {"name": "Fargo", "slug": "fargo", "id": "51781", "release": 1996, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "227734614": {"name": "Middleditch & Schwartz", "slug": "middleditch-schwartz", "id": "617744", "release": 2020, "runtime": 154, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "293598918": {"name": "Jaws", "slug": "jaws", "id": "51542", "release": 1975, "runtime": 124, "actions": {"rewatched": true, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "225876393": {"name": "Jaws 2", "slug": "jaws-2", "id": "51541", "release": 1978, "runtime": 117, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "225876305": {"name": "Jaws", "slug": "jaws", "id": "51542", "release": 1975, "runtime": 124, "actions": {"rewatched": true, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "225562896": {"name": "The Alpinist", "slug": "the-alpinist", "id": "606836", "release": 2021, "runtime": 93, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "225123063": {"name": "Bad Teacher", "slug": "bad-teacher", "id": "13439", "release": 2011, "runtime": 92, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "224765264": {"name": "Return of the Jedi", "slug": "return-of-the-jedi", "id": "50712", "release": 1983, "runtime": 132, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "223987717": {"name": "Scary Movie 4", "slug": "scary-movie-4", "id": "49433", "release": 2006, "runtime": 83, "actions": {"rewatched": false, "rating": 2, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "223872342": {"name": "A Very Harold & Kumar Christmas", "slug": "a-very-harold-kumar-christmas", "id": "10675", "release": 2011, "runtime": 90, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2022, "month": 1, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "220996898": {"name": "Don't Look Up", "slug": "dont-look-up-2021", "id": "572255", "release": 2021, "runtime": 138, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "220778000": {"name": "Licorice Pizza", "slug": "licorice-pizza", "id": "641086", "release": 2021, "runtime": 133, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "220308617": {"name": "The Florida Project", "slug": "the-florida-project", "id": "328538", "release": 2017, "runtime": 112, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "219930614": {"name": "James <PERSON>: Repert<PERSON>", "slug": "james-acaster-repertoire", "id": "445120", "release": 2018, "runtime": 205, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "219101562": {"name": "The Polar Express", "slug": "the-polar-express", "id": "48883", "release": 2004, "runtime": 100, "actions": {"rewatched": true, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "218994313": {"name": "Raiders of the Lost Ark", "slug": "raiders-of-the-lost-ark", "id": "51965", "release": 1981, "runtime": 115, "actions": {"rewatched": true, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "218816665": {"name": "<PERSON><PERSON><PERSON> Unchained", "slug": "django-unchained", "id": "52516", "release": 2012, "runtime": 165, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "218774368": {"name": "Santa Claus Is Comin' to Town", "slug": "santa-claus-is-comin-to-town", "id": "43919", "release": 1970, "runtime": 51, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "218473478": {"name": "Grandma Got Run Over by a Reindeer", "slug": "grandma-got-run-over-by-a-reindeer", "id": "14294", "release": 2000, "runtime": 51, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "218189298": {"name": "Fist of Fury", "slug": "fist-of-fury", "id": "45041", "release": 1972, "runtime": 108, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "217971786": {"name": "Spider-Man: No Way Home", "slug": "spider-man-no-way-home", "id": "560787", "release": 2021, "runtime": 148, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 21}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "217583181": {"name": "<PERSON>: What.", "slug": "bo-burnham-what", "id": "170860", "release": 2013, "runtime": 60, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 19}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "217071074": {"name": "<PERSON>: <PERSON><PERSON>", "slug": "seth-me<PERSON>-lobby-baby", "id": "568129", "release": 2019, "runtime": 61, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "216789501": {"name": "There Will Be Blood", "slug": "there-will-be-blood", "id": "48044", "release": 2007, "runtime": 158, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "216125028": {"name": "<PERSON>: The Age of Spin", "slug": "dave-chappelle-the-age-of-spin", "id": "377211", "release": 2017, "runtime": 67, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "216124747": {"name": "<PERSON>: I'm Telling <PERSON> for the Last Time", "slug": "jerry-seinfeld-im-telling-you-for-the-last-time", "id": "37436", "release": 1998, "runtime": 75, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "216124691": {"name": "Good Time", "slug": "good-time", "id": "362362", "release": 2017, "runtime": 102, "actions": {"rewatched": true, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 12, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "211652294": {"name": "Pumping Iron", "slug": "pumping-iron", "id": "48903", "release": 1977, "runtime": 86, "actions": {"rewatched": false, "rating": 7, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 23}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "211463218": {"name": "Knives Out", "slug": "knives-out-2019", "id": "475370", "release": 2019, "runtime": 131, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "209094462": {"name": "Talladega Nights: The Ballad of <PERSON>", "slug": "talladega-nights-the-ballad-of-ricky-bobby", "id": "46887", "release": 2006, "runtime": 108, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "208866540": {"name": "Last Night in Soho", "slug": "last-night-in-soho", "id": "505000", "release": 2021, "runtime": 116, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/7/", "no": 7}}, "207291331": {"name": "The French Dispatch", "slug": "the-french-dispatch", "id": "471207", "release": 2021, "runtime": 108, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "206676058": {"name": "The Empire Strikes Back", "slug": "the-empire-strikes-back", "id": "50713", "release": 1980, "runtime": 124, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 11, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "202740427": {"name": "The Babysitter: <PERSON> Queen", "slug": "the-babysitter-killer-queen", "id": "550049", "release": 2020, "runtime": 101, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "202564025": {"name": "Star Wars", "slug": "star-wars", "id": "2706", "release": 1977, "runtime": 121, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 15}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "202159101": {"name": "Star Wars: Episode III – Revenge of the Sith", "slug": "star-wars-episode-iii-revenge-of-the-sith", "id": "50709", "release": 2005, "runtime": 140, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 13}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "201733850": {"name": "Step Brothers", "slug": "step-brothers", "id": "44768", "release": 2008, "runtime": 98, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "201428562": {"name": "Wild Wild Country", "slug": "wild-wild-country", "id": "453524", "release": 2018, "runtime": 400, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "200867705": {"name": "<PERSON><PERSON><PERSON>", "slug": "icarus-2017", "id": "366068", "release": 2017, "runtime": 121, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "200718870": {"name": "Project X", "slug": "project-x-2012", "id": "8933", "release": 2012, "runtime": 88, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "200751447": {"name": "Squid Game", "slug": "squid-game", "id": "787181", "release": 2021, "runtime": 495, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "199406486": {"name": "Star Wars: Episode II – Attack of the Clones", "slug": "star-wars-episode-ii-attack-of-the-clones", "id": "50710", "release": 2002, "runtime": 142, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 10, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "198198939": {"name": "Star Wars: Episode I – The Phantom Menace", "slug": "star-wars-episode-i-the-phantom-menace", "id": "50711", "release": 1999, "runtime": 136, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 9, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "194906874": {"name": "AlphaGo", "slug": "alphago", "id": "387051", "release": 2017, "runtime": 90, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 9, "day": 9}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "194398802": {"name": "<PERSON> & The Sack Lunch Bunch", "slug": "john-mulaney-the-sack-lunch-bunch", "id": "575704", "release": 2019, "runtime": 70, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 9, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "192762907": {"name": "Enemy of the State", "slug": "enemy-of-the-state", "id": "46814", "release": 1998, "runtime": 132, "actions": {"rewatched": false, "rating": 4, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "192544761": {"name": "The Circle", "slug": "the-circle-2017", "id": "269475", "release": 2017, "runtime": 110, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "192532748": {"name": "Fast Times at Ridgemont High", "slug": "fast-times-at-ridgemont-high", "id": "43969", "release": 1982, "runtime": 90, "actions": {"rewatched": false, "rating": null, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "187822089": {"name": "<PERSON>: Alive from New York", "slug": "pete-da<PERSON><PERSON>-alive-from-new-york", "id": "598308", "release": 2020, "runtime": 49, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 6}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "187554283": {"name": "<PERSON>: <PERSON> at Radio City", "slug": "john-mulaney-kid-gorgeous-at-radio-city", "id": "450315", "release": 2018, "runtime": 65, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "187232032": {"name": "Fyre", "slug": "fyre-2019", "id": "496262", "release": 2019, "runtime": 98, "actions": {"rewatched": false, "rating": 5, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 8, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "186070139": {"name": "Big Time Adolescence", "slug": "big-time-adolescence", "id": "468709", "release": 2019, "runtime": 91, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 7, "day": 29}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "185067610": {"name": "The Bling Ring", "slug": "the-bling-ring", "id": "79893", "release": 2013, "runtime": 90, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 7, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "182433640": {"name": "The Social Network", "slug": "the-social-network", "id": "26711", "release": 2010, "runtime": 121, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 7, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "179205532": {"name": "<PERSON> and the Holy Grail", "slug": "monty-python-and-the-holy-grail", "id": "51390", "release": 1975, "runtime": 91, "actions": {"rewatched": false, "rating": 8, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "178275796": {"name": "Zombieland", "slug": "zombieland", "id": "39352", "release": 2009, "runtime": 88, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 22}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "175686678": {"name": "Spirited Away", "slug": "spirited-away", "id": "51921", "release": 2001, "runtime": 125, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 8}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "174983146": {"name": "<PERSON>ham: Inside", "slug": "bo-burnham-inside", "id": "739775", "release": 2021, "runtime": 87, "actions": {"rewatched": false, "rating": 9, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "174638102": {"name": "<PERSON>: The Comeback Kid", "slug": "john-mulaney-the-comeback-kid", "id": "303704", "release": 2015, "runtime": 62, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 3}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "174409695": {"name": "<PERSON>: Make Happy", "slug": "bo-burnham-make-happy", "id": "335216", "release": 2016, "runtime": 60, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 6, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "172886758": {"name": "The Mitchells vs. the Machines", "slug": "the-mitchells-vs-the-machines", "id": "431888", "release": 2021, "runtime": 110, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 5, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "171175319": {"name": "The Departed", "slug": "the-departed", "id": "51042", "release": 2006, "runtime": 151, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 5, "day": 16}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "170312894": {"name": "30 Minutes or Less", "slug": "30-minutes-or-less", "id": "4558", "release": 2011, "runtime": 83, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 5, "day": 12}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "169252782": {"name": "Good Burger", "slug": "good-burger", "id": "42794", "release": 1997, "runtime": 95, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 5, "day": 7}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "165077903": {"name": "Good Time", "slug": "good-time", "id": "362362", "release": 2017, "runtime": 102, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 4, "day": 17}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "161859547": {"name": "<PERSON>", "slug": "lady-bird", "id": "326279", "release": 2017, "runtime": 94, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 4, "day": 2}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "160909030": {"name": "Bad Trip", "slug": "bad-trip-2021", "id": "506972", "release": 2021, "runtime": 87, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 3, "day": 28}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "158812704": {"name": "<PERSON> Boy", "slug": "honey-boy", "id": "441870", "release": 2019, "runtime": 94, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 3, "day": 18}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "157290362": {"name": "Palm Springs", "slug": "palm-springs-2020", "id": "515467", "release": 2020, "runtime": 90, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 3, "day": 11}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "156168671": {"name": "The Perks of Being a Wallflower", "slug": "the-perks-of-being-a-wallflower", "id": "71338", "release": 2012, "runtime": 103, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 3, "day": 5}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "155466822": {"name": "The Truman Show", "slug": "the-truman-show", "id": "27256", "release": 1998, "runtime": 103, "actions": {"rewatched": false, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 3, "day": 1}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "155015177": {"name": "The Hunchback of Notre Dame", "slug": "the-hunchback-of-notre-dame-1996", "id": "46127", "release": 1996, "runtime": 91, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 2, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "152420824": {"name": "I'm Thinking of Ending Things", "slug": "im-thinking-of-ending-things", "id": "430806", "release": 2020, "runtime": 135, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": true}, "date": {"year": 2021, "month": 2, "day": 14}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "150195609": {"name": "300", "slug": "300", "id": "51111", "release": 2006, "runtime": 116, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2021, "month": 2, "day": 4}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "147084413": {"name": "Soul", "slug": "soul-2020", "id": "438511", "release": 2020, "runtime": 101, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2021, "month": 1, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "140531852": {"name": "The Christmas Chronicles", "slug": "the-christmas-chronicles", "id": "456712", "release": 2018, "runtime": 104, "actions": {"rewatched": false, "rating": 6, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 12, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "140436289": {"name": "The Grinch", "slug": "the-grinch", "id": "292688", "release": 2018, "runtime": 85, "actions": {"rewatched": false, "rating": 3, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 12, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "140419343": {"name": "The Polar Express", "slug": "the-polar-express", "id": "48883", "release": 2004, "runtime": 100, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 12, "day": 24}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "135785285": {"name": "How the Grinch Stole Christmas!", "slug": "how-the-grinch-stole-christmas", "id": "43940", "release": 1966, "runtime": 26, "actions": {"rewatched": true, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 11, "day": 27}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "135614379": {"name": "A <PERSON> Thanksgiving", "slug": "a-charlie-brown-thanksgiving", "id": "43857", "release": 1973, "runtime": 25, "actions": {"rewatched": true, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 11, "day": 26}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "135486011": {"name": "Spider-Man: Into the Spider-Verse", "slug": "spider-man-into-the-spider-verse", "id": "251943", "release": 2018, "runtime": 117, "actions": {"rewatched": true, "rating": 10, "liked": true, "reviewed": false}, "date": {"year": 2020, "month": 11, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/8/", "no": 8}}, "133232081": {"name": "<PERSON> Breaks the Internet", "slug": "ralph-breaks-the-internet", "id": "338883", "release": 2018, "runtime": 112, "actions": {"rewatched": false, "rating": 7, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 11, "day": 10}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/9/", "no": 9}}, "130565752": {"name": "<PERSON><PERSON>", "slug": "jojo-rabbit", "id": "444600", "release": 2019, "runtime": 108, "actions": {"rewatched": false, "rating": 9, "liked": true, "reviewed": false}, "date": {"year": 2020, "month": 10, "day": 25}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/9/", "no": 9}}, "129707465": {"name": "mid90s", "slug": "mid90s", "id": "370451", "release": 2018, "runtime": 86, "actions": {"rewatched": false, "rating": 8, "liked": false, "reviewed": false}, "date": {"year": 2020, "month": 10, "day": 20}, "page": {"url": "https://letterboxd.com/nmcassa/films/diary/page/9/", "no": 9}}}, "count": 403, "last_page": 9}