<h2 id="user_lists">user_lists(user object)</h2>

```python
from letterboxdpy import user
user_instance = user.User("nmcassa")
print(user.user_lists(user_instance))
```

<details>
  <summary>Click to expand <code>user_lists</code> method response</summary>

```json
{
  "lists": {
    "30052453": {
      "title": "DEF CON Movie List",
      "slug": "def-con-movie-list",
      "description": "The DEF CON Hacking Conference's suggested movie list. defcon.org/html/links/movie-list.html",
      "url": "https://letterboxd.com/nmcassa/list/def-con-movie-list/",
      "count": 11,
      "likes": 0,
      "comments": 0
    }
  },
  "count": 1,
  "last_page": 1
}
```
</details>